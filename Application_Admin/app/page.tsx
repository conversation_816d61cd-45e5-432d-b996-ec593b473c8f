"use client";

import { useState, useEffect } from "react";
import { LoginForm } from "@/components/auth/login-form";
import { Sidebar } from "@/components/ui/sidebar";
import { StatsCards } from "@/components/dashboard/stats-cards";
import { RecentOrders } from "@/components/dashboard/recent-orders";
import { SalesChart } from "@/components/dashboard/sales-chart";
import { ProductTable } from "@/components/products/product-table";
import { AddProductForm } from "@/components/products/add-product-form";
import { CategoryTable } from "@/components/categories/category-table";
import { AddCategoryForm } from "@/components/categories/add-category-form";
import { OrderTable } from "@/components/orders/order-table";
import { CustomerTable } from "@/components/customers/customer-table";
import { InventoryTable } from "@/components/inventory/inventory-table";
import { authService } from "@/lib/services";
import { DollarSign, ShoppingCart, Users, BarChart3 } from "lucide-react";
import { Toaster } from "sonner";

export default function AdminPanel() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [currentPage, setCurrentPage] = useState("dashboard");
  const [showAddProduct, setShowAddProduct] = useState(false);
  const [showAddCategory, setShowAddCategory] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if user is already logged in
    const checkAuth = async () => {
      try {
        const isValid = await authService.checkAuth();
        if (isValid) {
          const profile = await authService.getProfile();
          if (profile.user.role === 'admin') {
            setIsAuthenticated(true);
            setCurrentUser(profile.user);
          } else {
            authService.logout();
          }
        }
      } catch (error) {
        console.error('Auth check failed:', error);
        authService.logout();
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  const handleLogin = (user: any) => {
    setIsAuthenticated(true);
    setCurrentUser(user);
  };

  const handleLogout = () => {
    authService.logout();
    setIsAuthenticated(false);
    setCurrentUser(null);
    setCurrentPage("dashboard");
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <LoginForm onLogin={handleLogin} />;
  }

  const handleNavigate = (page: string) => {
    setCurrentPage(page);
    setShowAddProduct(false);
    setShowAddCategory(false);
  };

  const handleProductAdded = () => {
    setShowAddProduct(false);
    // Refresh products if on products page
    if (currentPage === 'products') {
      window.location.reload();
    }
  };

  const handleCategoryAdded = () => {
    setShowAddCategory(false);
    // Refresh categories if on categories page
    if (currentPage === 'categories') {
      window.location.reload();
    }
  };

  const renderContent = () => {
    switch (currentPage) {
      case "dashboard":
        return (
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold">Dashboard</h1>
              <p className="text-muted-foreground">Welcome back! Here's what's happening with your store.</p>
            </div>
            <StatsCards />
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <SalesChart />
              <RecentOrders />
            </div>
          </div>
        );
      case "products":
        return (
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold">Products</h1>
              <p className="text-muted-foreground">Manage your product catalog and inventory.</p>
            </div>
            <ProductTable onAddProduct={() => setShowAddProduct(true)} />
          </div>
        );
      case "categories":
        return (
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold">Categories</h1>
              <p className="text-muted-foreground">Organize your products with categories.</p>
            </div>
            <CategoryTable onAddCategory={() => setShowAddCategory(true)} />
          </div>
        );
      case "orders":
        return (
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold">Orders</h1>
              <p className="text-muted-foreground">Track and manage customer orders.</p>
            </div>
            <OrderTable />
          </div>
        );
      case "customers":
        return (
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold">Customers</h1>
              <p className="text-muted-foreground">Manage your customer base and relationships.</p>
            </div>
            <CustomerTable />
          </div>
        );
      case "inventory":
        return (
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold">Inventory</h1>
              <p className="text-muted-foreground">Track stock levels and manage inventory.</p>
            </div>
            <InventoryTable />
          </div>
        );
      case "analytics":
        return (
          <div className="space-y-6">
            <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
              <h1 className="text-3xl font-bold text-slate-900">Analytics Dashboard</h1>
              <p className="text-slate-600 mt-2">Comprehensive insights into your store performance and customer behavior.</p>
            </div>

            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl p-6 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-100">Total Revenue</p>
                    <p className="text-3xl font-bold">₹2,45,680</p>
                    <p className="text-blue-100 text-sm">+12% from last month</p>
                  </div>
                  <div className="bg-white/20 p-3 rounded-lg">
                    <DollarSign className="w-8 h-8" />
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-xl p-6 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-100">Total Orders</p>
                    <p className="text-3xl font-bold">1,247</p>
                    <p className="text-green-100 text-sm">+8% from last month</p>
                  </div>
                  <div className="bg-white/20 p-3 rounded-lg">
                    <ShoppingCart className="w-8 h-8" />
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl p-6 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-purple-100">New Customers</p>
                    <p className="text-3xl font-bold">342</p>
                    <p className="text-purple-100 text-sm">+15% from last month</p>
                  </div>
                  <div className="bg-white/20 p-3 rounded-lg">
                    <Users className="w-8 h-8" />
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl p-6 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-orange-100">Conversion Rate</p>
                    <p className="text-3xl font-bold">3.2%</p>
                    <p className="text-orange-100 text-sm">+0.5% from last month</p>
                  </div>
                  <div className="bg-white/20 p-3 rounded-lg">
                    <BarChart3 className="w-8 h-8" />
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <SalesChart />
              <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
                <h3 className="text-xl font-semibold text-slate-900 mb-4">Top Products</h3>
                <div className="space-y-4">
                  {[
                    { name: "Ganesha Murti", sales: 156, revenue: "₹45,680" },
                    { name: "Krishna Idol", sales: 134, revenue: "₹38,420" },
                    { name: "Lakshmi Statue", sales: 98, revenue: "₹29,340" },
                    { name: "Hanuman Murti", sales: 87, revenue: "₹26,100" },
                  ].map((product, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                      <div>
                        <p className="font-medium text-slate-900">{product.name}</p>
                        <p className="text-sm text-slate-600">{product.sales} units sold</p>
                      </div>
                      <p className="font-semibold text-green-600">{product.revenue}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
                <h3 className="text-xl font-semibold text-slate-900 mb-4">Customer Insights</h3>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-slate-600">Returning Customers</span>
                    <span className="font-semibold">68%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-600">Average Order Value</span>
                    <span className="font-semibold">₹1,847</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-600">Customer Lifetime Value</span>
                    <span className="font-semibold">₹8,420</span>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
                <h3 className="text-xl font-semibold text-slate-900 mb-4">Traffic Sources</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-slate-600">Direct</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-20 bg-slate-200 rounded-full h-2">
                        <div className="bg-blue-500 h-2 rounded-full" style={{width: '45%'}}></div>
                      </div>
                      <span className="text-sm font-medium">45%</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-slate-600">Search</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-20 bg-slate-200 rounded-full h-2">
                        <div className="bg-green-500 h-2 rounded-full" style={{width: '32%'}}></div>
                      </div>
                      <span className="text-sm font-medium">32%</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-slate-600">Social</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-20 bg-slate-200 rounded-full h-2">
                        <div className="bg-purple-500 h-2 rounded-full" style={{width: '23%'}}></div>
                      </div>
                      <span className="text-sm font-medium">23%</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
                <h3 className="text-xl font-semibold text-slate-900 mb-4">Recent Activity</h3>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm text-slate-600">New order #1247 received</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-sm text-slate-600">Customer John D. registered</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                    <span className="text-sm text-slate-600">Product "Ganesha Murti" low stock</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span className="text-sm text-slate-600">Payment of ₹2,340 received</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      case "settings":
        return (
          <div className="space-y-6">
            <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
              <h1 className="text-3xl font-bold text-slate-900">Settings</h1>
              <p className="text-slate-600 mt-2">Configure your store settings and preferences.</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Settings Navigation */}
              <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
                <h3 className="text-lg font-semibold text-slate-900 mb-4">Settings Categories</h3>
                <div className="space-y-2">
                  {[
                    { name: "General", active: true },
                    { name: "Payment", active: false },
                    { name: "Shipping", active: false },
                    { name: "Notifications", active: false },
                    { name: "Security", active: false },
                    { name: "API Keys", active: false },
                  ].map((item, index) => (
                    <button
                      key={index}
                      className={`w-full text-left px-4 py-3 rounded-lg transition-colors ${
                        item.active
                          ? "bg-blue-50 text-blue-700 border border-blue-200"
                          : "text-slate-600 hover:bg-slate-50"
                      }`}
                    >
                      {item.name}
                    </button>
                  ))}
                </div>
              </div>

              {/* Settings Content */}
              <div className="lg:col-span-2 space-y-6">
                {/* General Settings */}
                <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
                  <h3 className="text-xl font-semibold text-slate-900 mb-4">General Settings</h3>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-2">Store Name</label>
                      <input
                        type="text"
                        defaultValue="Ghanshyam Murti Bhandar"
                        className="w-full px-4 py-2 border border-slate-200 rounded-lg focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-2">Store Description</label>
                      <textarea
                        rows={3}
                        defaultValue="Premium religious idols and spiritual items for your devotional needs."
                        className="w-full px-4 py-2 border border-slate-200 rounded-lg focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-2">Currency</label>
                        <select className="w-full px-4 py-2 border border-slate-200 rounded-lg focus:border-blue-500 focus:ring-blue-500">
                          <option value="INR">Indian Rupee (₹)</option>
                          <option value="USD">US Dollar ($)</option>
                          <option value="EUR">Euro (€)</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-2">Timezone</label>
                        <select className="w-full px-4 py-2 border border-slate-200 rounded-lg focus:border-blue-500 focus:ring-blue-500">
                          <option value="Asia/Kolkata">Asia/Kolkata (IST)</option>
                          <option value="UTC">UTC</option>
                          <option value="America/New_York">America/New_York (EST)</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Store Information */}
                <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
                  <h3 className="text-xl font-semibold text-slate-900 mb-4">Store Information</h3>
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-2">Contact Email</label>
                        <input
                          type="email"
                          defaultValue="<EMAIL>"
                          className="w-full px-4 py-2 border border-slate-200 rounded-lg focus:border-blue-500 focus:ring-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-2">Phone Number</label>
                        <input
                          type="tel"
                          defaultValue="+91 98765 43210"
                          className="w-full px-4 py-2 border border-slate-200 rounded-lg focus:border-blue-500 focus:ring-blue-500"
                        />
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-2">Store Address</label>
                      <textarea
                        rows={3}
                        defaultValue="123 Temple Street, Religious Quarter, Mumbai, Maharashtra 400001, India"
                        className="w-full px-4 py-2 border border-slate-200 rounded-lg focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                </div>

                {/* Business Hours */}
                <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
                  <h3 className="text-xl font-semibold text-slate-900 mb-4">Business Hours</h3>
                  <div className="space-y-3">
                    {[
                      { day: "Monday", open: "09:00", close: "18:00", closed: false },
                      { day: "Tuesday", open: "09:00", close: "18:00", closed: false },
                      { day: "Wednesday", open: "09:00", close: "18:00", closed: false },
                      { day: "Thursday", open: "09:00", close: "18:00", closed: false },
                      { day: "Friday", open: "09:00", close: "18:00", closed: false },
                      { day: "Saturday", open: "09:00", close: "16:00", closed: false },
                      { day: "Sunday", open: "", close: "", closed: true },
                    ].map((schedule, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                        <span className="font-medium text-slate-700 w-20">{schedule.day}</span>
                        {schedule.closed ? (
                          <span className="text-red-600 font-medium">Closed</span>
                        ) : (
                          <div className="flex items-center space-x-2">
                            <input
                              type="time"
                              defaultValue={schedule.open}
                              className="px-3 py-1 border border-slate-200 rounded text-sm"
                            />
                            <span className="text-slate-500">to</span>
                            <input
                              type="time"
                              defaultValue={schedule.close}
                              className="px-3 py-1 border border-slate-200 rounded text-sm"
                            />
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Save Button */}
                <div className="flex justify-end">
                  <button className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium">
                    Save Changes
                  </button>
                </div>
              </div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex h-screen bg-slate-50">
      <Sidebar
        currentPage={currentPage}
        onNavigate={handleNavigate}
        onLogout={handleLogout}
        currentUser={currentUser}
      />
      <main className="flex-1 overflow-auto">
        <div className="container mx-auto p-6 md:ml-4">
          {renderContent()}
        </div>
      </main>

      {/* Add Product Modal */}
      {showAddProduct && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <AddProductForm
              onClose={() => setShowAddProduct(false)}
              onProductAdded={handleProductAdded}
            />
          </div>
        </div>
      )}

      {/* Add Category Modal */}
      {showAddCategory && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <AddCategoryForm
              onClose={() => setShowAddCategory(false)}
              onCategoryAdded={handleCategoryAdded}
            />
          </div>
        </div>
      )}

      <Toaster position="top-right" />
    </div>
  );
}